"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import { NotificationDropdown } from "@/components/notifications/notification-dropdown"
import { UserNav } from "@/components/layout/user-nav"
import {
  Search,
  Menu,
  ChevronDown,
  Home,
  BookOpen,
  HelpCircle,
  MessageSquare,
  FileText,
  Users,
  Settings,
  UserIcon,
  Info,
  List,
  PlusCircle,
  LayoutDashboard,
  Folder,
  FileBarChart,
  Activity,
  User,
  Contact,
  BookMarked,
} from "lucide-react"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import { HoverCardEffect } from "@/components/ui/hover-card-effect"
import { useEffect, useState } from "react"
import Image from "next/image"

interface UnifiedTopbarProps {
  user?: {
    id: string
    email: string
    full_name: string
    role?: string
    avatar_url?: string
  } | null
  showSearch?: boolean
  showMobileNav?: boolean
  className?: string
}

export function UnifiedTopbar({ user, showSearch = true, showMobileNav = true, className }: UnifiedTopbarProps) {
  const pathname = usePathname()
  const isAuthenticated = !!user
  const [scrolled, setScrolled] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  // Xử lý hiệu ứng scroll
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Các mục menu công khai (hiển thị cho tất cả người dùng)
  const publicNavItems = [
    { label: "Trang chủ", href: "/", icon: <Home className="h-4 w-4 mr-1.5" /> },
    { label: "Giới thiệu", href: "/about", icon: <Info className="h-4 w-4 mr-1.5" /> },
    {
      label: "Thư viện kiến thức",
      href: "/knowledge",
      icon: <BookOpen className="h-4 w-4 mr-1.5" />,
      active: pathname.startsWith("/knowledge"),
      dropdown: true,
      items: [
        { label: "Bài viết mới nhất", href: "/knowledge", icon: <BookMarked className="h-4 w-4 mr-1.5" /> },
        { label: "Danh mục", href: "/knowledge/categories", icon: <Folder className="h-4 w-4 mr-1.5" /> },
        { label: "Tìm kiếm", href: "/knowledge/search", icon: <Search className="h-4 w-4 mr-1.5" /> },
      ],
    },
    { label: "Tài nguyên", href: "/resources", icon: <BookMarked className="h-4 w-4 mr-1.5" /> },
    { label: "Liên hệ", href: "/contact", icon: <Contact className="h-4 w-4 mr-1.5" /> },
  ]

  // Các mục menu dành cho người dùng đã đăng nhập
  const authenticatedNavItems = [
    {
      label: "Tổng quan",
      href: "/dashboard",
      icon: <LayoutDashboard className="h-4 w-4 mr-1.5" />,
      active: pathname === "/dashboard",
    },
    {
      label: "Hồ sơ y tế",
      href: "/medical-records",
      icon: <FileText className="h-4 w-4 mr-1.5" />,
      active:
        pathname.startsWith("/medical-records") ||
        pathname.startsWith("/prescriptions") ||
        pathname.startsWith("/health-metrics") ||
        pathname.startsWith("/symptoms"),
      dropdown: true,
      items: [
        { label: "Hồ sơ y tế", href: "/medical-records", icon: <Folder className="h-4 w-4 mr-1.5" /> },
        { label: "Đơn thuốc", href: "/prescriptions", icon: <FileText className="h-4 w-4 mr-1.5" /> },
        { label: "Chỉ số sức khỏe", href: "/health-metrics", icon: <FileBarChart className="h-4 w-4 mr-1.5" /> },
        { label: "Triệu chứng", href: "/symptoms", icon: <Activity className="h-4 w-4 mr-1.5" /> },
      ],
    },
    {
      label: "Tư vấn",
      href: "/advisory",
      icon: <HelpCircle className="h-4 w-4 mr-1.5" />,
      active: pathname.startsWith("/advisory"),
      dropdown: true,
      items: [
        { label: "Danh sách tư vấn", href: "/advisory", icon: <List className="h-4 w-4 mr-1.5" /> },
        { label: "Yêu cầu tư vấn", href: "/advisory/request", icon: <PlusCircle className="h-4 w-4 mr-1.5" /> },
      ],
    },
    {
      label: "Hội chẩn",
      href: "/consultations",
      icon: <HelpCircle className="h-4 w-4 mr-1.5" />,
      active: pathname.startsWith("/consultations"),
      dropdown: true,
      items: [
        { label: "Danh sách hội chẩn", href: "/consultations", icon: <List className="h-4 w-4 mr-1.5" /> },
        { label: "Yêu cầu hội chẩn", href: "/consultations/request", icon: <PlusCircle className="h-4 w-4 mr-1.5" /> },
      ],
    },
    {
      label: "Tin nhắn",
      href: "/messages",
      icon: <MessageSquare className="h-4 w-4 mr-1.5" />,
      active: pathname.startsWith("/messages"),
      badge: 3,
    },
    {
      label: "Cộng đồng",
      href: "/connect",
      icon: <Users className="h-4 w-4 mr-1.5" />,
      active: pathname.startsWith("/connect"),
    },
  ]

  // Thêm mục quản trị nếu người dùng là admin
  if (user?.role === "admin") {
    authenticatedNavItems.push({
      label: "Quản trị",
      href: "/admin",
      icon: <Settings className="h-4 w-4 mr-1.5" />,
      active: pathname.startsWith("/admin"),
      dropdown: true,
      items: [
        { label: "Tổng quan", href: "/admin", icon: <LayoutDashboard className="h-4 w-4 mr-1.5" /> },
        { label: "Người dùng", href: "/admin/users", icon: <Users className="h-4 w-4 mr-1.5" /> },
        { label: "Bài viết", href: "/admin/articles", icon: <FileText className="h-4 w-4 mr-1.5" /> },
        { label: "Tư vấn", href: "/admin/advisory", icon: <HelpCircle className="h-4 w-4 mr-1.5" /> },
        { label: "Cài đặt", href: "/admin/settings", icon: <Settings className="h-4 w-4 mr-1.5" /> },
      ],
    })
  }

  // Chọn các mục menu hiển thị dựa trên trạng thái đăng nhập và đường dẫn hiện tại
  const navItems =
    pathname.startsWith("/dashboard") ||
      pathname.startsWith("/medical-records") ||
      pathname.startsWith("/advisory") ||
      pathname.startsWith("/messages") ||
      pathname.startsWith("/connect") ||
      pathname.startsWith("/admin") ||
      pathname.startsWith("/prescriptions") ||
      pathname.startsWith("/health-metrics") ||
      pathname.startsWith("/symptoms") ||
      pathname.startsWith("/appointments")
      ? authenticatedNavItems
      : publicNavItems

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full border-b transition-all duration-200",
        scrolled
          ? "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-sm"
          : "bg-background",
        className,
      )}
    >
      <div className="container flex h-16 items-center">
        <div className="flex items-center gap-6 mr-auto">
          {showMobileNav && (
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[400px] p-0">
                <div className="p-4 border-b">
                  <Link href="/" className="flex items-center space-x-2">
                    <div className="relative w-8 h-8">
                      <Image src="/logo-icon.png" alt="OncoCare.VN Logo" fill className="object-contain" />
                    </div>
                    <span className="font-bold text-xl">OncoCare.VN</span>
                  </Link>
                </div>
                <div className="py-4 px-1">
                  {isAuthenticated && (
                    <div className="px-4 py-2 mb-2">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="relative w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          {user?.avatar_url ? (
                            <Image
                              src={user.avatar_url || "/placeholder.svg"}
                              alt={user.full_name}
                              fill
                              className="rounded-full object-cover"
                            />
                          ) : (
                            <span className="text-primary font-medium">
                              {user?.full_name
                                ?.split(" ")
                                .map((n) => n[0])
                                .join("")
                                .toUpperCase()
                                .substring(0, 2)}
                            </span>
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{user?.full_name}</p>
                          <p className="text-xs text-muted-foreground">{user?.email}</p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <Link href="/connect/profile">
                          <Button variant="outline" size="sm" className="w-full">
                            <User className="h-4 w-4 mr-1.5" />
                            Hồ sơ
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={async () => {
                            const supabase = (await import("@/lib/supabase/client")).createClient()
                            await supabase.auth.signOut()
                            window.location.href = "/login"
                          }}
                        >
                          Đăng xuất
                        </Button>
                      </div>
                    </div>
                  )}
                  <nav className="space-y-1">
                    {navItems.map((item) => (
                      <div key={item.href}>
                        {item.dropdown ? (
                          <div className="mb-1">
                            <button
                              onClick={() => setActiveDropdown(activeDropdown === item.href ? null : item.href)}
                              className={cn(
                                "w-full flex items-center justify-between px-4 py-2 text-sm rounded-md",
                                item.active
                                  ? "bg-primary/10 text-primary font-medium"
                                  : "hover:bg-muted text-foreground",
                              )}
                            >
                              <div className="flex items-center">
                                {item.icon}
                                <span>{item.label}</span>
                              </div>
                              <ChevronDown
                                className={cn(
                                  "h-4 w-4 transition-transform",
                                  activeDropdown === item.href ? "transform rotate-180" : "",
                                )}
                              />
                            </button>
                            {activeDropdown === item.href && (
                              <div className="overflow-hidden">
                                <div className="pl-6 pr-2 py-1 space-y-1">
                                  {item.items?.map((subItem) => (
                                    <Link
                                      key={subItem.href}
                                      href={subItem.href}
                                      className={cn(
                                        "flex items-center px-3 py-1.5 text-sm rounded-md",
                                        pathname === subItem.href
                                          ? "bg-primary/5 text-primary"
                                          : "hover:bg-muted text-muted-foreground hover:text-foreground",
                                      )}
                                    >
                                      {subItem.icon}
                                      <span>{subItem.label}</span>
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <Link
                            href={item.href}
                            className={cn(
                              "flex items-center px-4 py-2 text-sm rounded-md",
                              item.active ? "bg-primary/10 text-primary font-medium" : "hover:bg-muted text-foreground",
                            )}
                          >
                            {item.icon}
                            <span>{item.label}</span>
                            {item.badge && (
                              <span className="ml-auto bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                {item.badge}
                              </span>
                            )}
                          </Link>
                        )}
                      </div>
                    ))}
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
          )}

          <Link href="/" className="flex items-center space-x-2">
            <div className="relative w-8 h-8 hidden sm:block">
              <Image src="/logo-icon.png" alt="OncoCare.VN Logo" fill className="object-contain" />
            </div>
            <HoverCardEffect className="rounded-md" tiltDegree={5}>
              <div className="relative group">
                <span className="logo-text text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">
                  OncoCare.VN
                </span>
              </div>
            </HoverCardEffect>
          </Link>

          <nav className="hidden md:flex items-center gap-1">
            {navItems.map((item) => (
              <div key={item.href} className="relative group">
                {item.dropdown ? (
                  <div className="relative group">
                    <button
                      className={cn(
                        "px-3 py-2 rounded-md text-sm font-medium flex items-center gap-1 transition-colors",
                        item.active ? "text-primary" : "text-foreground hover:text-primary hover:bg-muted/50",
                      )}
                      onMouseEnter={() => setActiveDropdown(item.href)}
                      onMouseLeave={() => setActiveDropdown(null)}
                      onClick={() => setActiveDropdown(activeDropdown === item.href ? null : item.href)}
                      aria-expanded={activeDropdown === item.href}
                    >
                      {item.icon}
                      {item.label}
                      <ChevronDown
                        className={cn(
                          "ml-0.5 h-4 w-4 transition-transform",
                          activeDropdown === item.href ? "transform rotate-180" : "",
                        )}
                      />
                    </button>
                    <div
                      className={cn(
                        "absolute left-0 top-full mt-1 w-56 rounded-md shadow-lg bg-popover ring-1 ring-black ring-opacity-5 z-50 transition-all duration-200 origin-top-left",
                        activeDropdown === item.href
                          ? "opacity-100 scale-100"
                          : "opacity-0 scale-95 pointer-events-none",
                      )}
                      onMouseEnter={() => setActiveDropdown(item.href)}
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      <div className="py-1 rounded-md bg-popover">
                        {item.items?.map((subItem) => (
                          <Link
                            key={subItem.href}
                            href={subItem.href}
                            className="flex items-center px-4 py-2 text-sm text-popover-foreground hover:bg-muted transition-colors"
                          >
                            {subItem.icon}
                            {subItem.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      "px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors",
                      item.active ? "text-primary" : "text-foreground hover:text-primary hover:bg-muted/50",
                    )}
                  >
                    {item.icon}
                    {item.label}
                    {item.badge && (
                      <span className="ml-1.5 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                )}
                {item.active && <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary" />}
              </div>
            ))}
          </nav>
        </div>

        <div className="flex items-center gap-2">
          {showSearch && (
            <Button
              variant="ghost"
              size="icon"
              className="text-foreground hover:text-primary hover:bg-muted/50"
              asChild
            >
              <Link href={pathname.startsWith("/knowledge") ? "/knowledge/search" : "/search"}>
                <Search className="h-5 w-5" />
                <span className="sr-only">Tìm kiếm</span>
              </Link>
            </Button>
          )}

          <ModeToggle />

          {isAuthenticated ? (
            <>
              <NotificationDropdown />
              <UserNav user={user} />
            </>
          ) : (
            <div className="flex items-center gap-2">
              <Link href="/login">
                <Button variant="ghost" size="sm" className="hidden sm:flex">
                  Đăng nhập
                </Button>
                <Button variant="ghost" size="icon" className="sm:hidden">
                  <UserIcon className="h-5 w-5" />
                </Button>
              </Link>
              <Link href="/login?tab=signup">
                <Button size="sm" className="bg-primary hover:bg-primary/90">
                  Đăng ký
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
